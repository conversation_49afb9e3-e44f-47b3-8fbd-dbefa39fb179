import { Api } from '@/typings/api';
import { request } from '../request';

/**
 * 获取应用列表
 * @param page 页码
 * @param pageSize 每页大小
 */
export function fetchAppList(page: number = 1, pageSize: number = 20) {
  // 模拟数据
  const mockData: Api.App.AppListResponse = {
    list: [
      {
        id: 1,
        appid: 'app_001',
        appkey: 'sk_test_1234567890abcdef',
        nickname: '测试应用1',
        created_at: '2025-06-20 10:30:00',
        updated_at: '2025-06-20 10:30:00'
      },
      {
        id: 2,
        appid: 'app_002',
        appkey: 'sk_test_abcdef1234567890',
        nickname: '生产应用',
        created_at: '2025-06-19 15:20:00',
        updated_at: '2025-06-20 09:15:00'
      },
      {
        id: 3,
        appid: 'app_003',
        appkey: 'sk_test_fedcba0987654321',
        nickname: '开发环境应用',
        created_at: '2025-06-18 14:10:00',
        updated_at: '2025-06-18 14:10:00'
      },
      {
        id: 4,
        appid: 'app_004',
        appkey: 'sk_test_1357924680abcdef',
        nickname: '移动端应用',
        created_at: '2025-06-17 11:45:00',
        updated_at: '2025-06-19 16:30:00'
      },
      {
        id: 5,
        appid: 'app_005',
        appkey: 'sk_test_2468135790fedcba',
        nickname: 'Web端应用',
        created_at: '2025-06-16 09:20:00',
        updated_at: '2025-06-20 08:45:00'
      }
    ],
    page,
    page_size: pageSize,
    total: 5
  };

  // 模拟分页
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedList = mockData.list.slice(startIndex, endIndex);

  return Promise.resolve({
    data: {
      ...mockData,
      list: paginatedList
    },
    error: null
  });
}

/**
 * 创建应用
 * @param data 创建应用请求数据
 */
export function fetchCreateApp(data: Api.App.CreateAppRequest) {
  // 模拟创建应用
  const newApp: Api.App.AppListItem = {
    id: Date.now(),
    appid: `app_${Date.now().toString().slice(-6)}`,
    appkey: `sk_test_${Math.random().toString(36).substring(2, 18)}`,
    nickname: data.nickname,
    created_at: new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).replace(/\//g, '-'),
    updated_at: new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).replace(/\//g, '-')
  };

  return Promise.resolve({
    data: newApp,
    error: null
  });
}

/**
 * 更新应用
 * @param data 更新应用请求数据
 */
export function fetchUpdateApp(data: Api.App.UpdateAppRequest) {
  // 模拟更新应用
  return Promise.resolve({
    data: {
      id: data.id,
      nickname: data.nickname,
      updated_at: new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(/\//g, '-')
    },
    error: null
  });
}

/**
 * 删除应用
 * @param id 应用ID
 */
export function fetchDeleteApp(id: number) {
  // 模拟删除应用
  return Promise.resolve({
    data: { success: true },
    error: null
  });
}

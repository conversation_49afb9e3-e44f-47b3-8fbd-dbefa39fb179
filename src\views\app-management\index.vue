<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <NCard title="应用管理" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <div class="flex items-center gap-12px">
          <!-- 调试信息 -->
          <NTag type="info" size="small">
            当前用户角色: {{ authStore.userInfo.role.join(', ') || '无' }}
          </NTag>
          <NButton type="primary" size="small" @click="handleAddApp">
            <template #icon>
              <icon-ic-round-plus class="text-icon" />
            </template>
            添加应用
          </NButton>
        </div>
      </template>
      <div class="h-full flex-col">
        <NDataTable
          :columns="columns"
          :data="data"
          size="small"
          :flex-height="!appStore.isMobile"
          :scroll-x="962"
          :loading="loading"
          remote
          :row-key="row => row.id"
          :pagination="pagination"
          @update:page="handlePageChange"
        />
      </div>
    </NCard>

    <!-- 添加/编辑应用弹窗 -->
    <NModal v-model:show="showModal" preset="card" :title="modalTitle" class="w-700px">
      <NForm ref="formRef" :model="formModel" :rules="rules" label-placement="left" :label-width="80">
        <div class="flex-col gap-16px">
          <NFormItem label="应用昵称" path="nickname">
            <NInput v-model:value="formModel.nickname" placeholder="请输入应用昵称" />
          </NFormItem>
        </div>
      </NForm>
      <template #footer>
        <div class="flex justify-end gap-12px">
          <NButton @click="closeModal">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
        </div>
      </template>
    </NModal>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, onMounted } from 'vue';
import type { Ref } from 'vue';
import { NButton, NPopconfirm, NTag } from 'naive-ui';
import type { DataTableColumns, FormInst, FormRules, PaginationProps } from 'naive-ui';
import { fetchAppList, fetchCreateApp, fetchUpdateApp, fetchDeleteApp } from '@/service/api';
import { useLoading } from '@sa/hooks';
import { useAppStore } from '@/store/modules/app';
import { useAuthStore } from '@/store/modules/auth';
import { $t } from '@/locales';
import { Api } from '@/typings/api';

defineOptions({
  name: 'AppManagement'
});

const appStore = useAppStore();
const authStore = useAuthStore();
const { loading, startLoading, endLoading } = useLoading();

// 表格数据
const data: Ref<Api.App.AppListItem[]> = ref([]);

// 分页配置
// 分页状态
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 50],
  showQuickJumper: true,
  onUpdatePage: (page: number) => {
    pagination.page = page;
    getAppData();
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    getAppData();
  }
});

// 表格列配置
const columns: Ref<DataTableColumns<Api.App.AppListItem>> = ref([
  {
    title: '序号',
    key: 'index',
    width: 80,
    render: (_, index) => {
      return (pagination.page - 1) * pagination.pageSize + index + 1;
    }
  },
  {
    title: 'App ID',
    key: 'appid',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: 'App Key',
    key: 'appkey',
    width: 200,
    ellipsis: {
      tooltip: true
    },
    render: (row) => {
      const maskedKey = row.appkey.substring(0, 8) + '****' + row.appkey.substring(row.appkey.length - 4);
      return (
        <NTag type="info" size="small">
          {maskedKey}
        </NTag>
      );
    }
  },
  {
    title: '应用昵称',
    key: 'nickname',
    width: 150,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 180
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render: (row) => {
      return (
        <div class="flex gap-8px">
          <NButton
            type="primary"
            size="small"
            onClick={() => handleEditApp(row)}
          >
            修改
          </NButton>
          <NPopconfirm
            onPositiveClick={() => handleDeleteApp(row.id)}
          >
            {{
              default: () => '确定删除这个应用吗？',
              trigger: () => (
                <NButton type="error" size="small">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      );
    }
  }
]);

// 弹窗相关
const showModal = ref(false);
const modalTitle = ref('');
const isEdit = ref(false);
const editingId = ref<number | null>(null);

// 表单相关
const formRef = ref<FormInst | null>(null);
const formModel = reactive({
  nickname: ''
});

const rules: FormRules = {
  nickname: [
    { required: true, message: '请输入应用昵称', trigger: 'blur' },
    { min: 2, max: 50, message: '应用昵称长度应在2-50个字符之间', trigger: 'blur' }
  ]
};

// 页面加载时获取数据
onMounted(() => {
  getAppData();
});

// 获取应用数据
async function getAppData() {
  startLoading();
  try {
    console.log('开始获取应用数据，页码:', pagination.page, '每页大小:', pagination.pageSize);
    const { data: appResponse, error } = await fetchAppList(
      pagination.page,
      pagination.pageSize
    );

    console.log('API响应结果:', { appResponse, error });

    if (!error && appResponse) {
      console.log('设置数据前 - data.value:', data.value);
      console.log('appResponse.list:', appResponse.list);
      data.value = appResponse.list || [];
      pagination.total = appResponse.total || 0;
      console.log('设置数据后 - data.value:', data.value);
      console.log('pagination.total:', pagination.total);
    } else {
      console.error('API返回错误:', error);
      window.$message?.error(typeof error === 'string' ? error : '获取应用数据失败');
      data.value = [];
    }
  } catch (error) {
    console.error('获取应用数据异常:', error);
    window.$message?.error('获取应用数据失败');
    data.value = [];
  } finally {
    endLoading();
  }
}

// 分页变化处理
function handlePageChange(page: number) {
  pagination.page = page;
  getAppData();
}

// 添加应用
function handleAddApp() {
  isEdit.value = false;
  modalTitle.value = '添加应用';
  resetForm();
  showModal.value = true;
}

// 编辑应用
function handleEditApp(app: Api.App.AppListItem) {
  isEdit.value = true;
  modalTitle.value = '编辑应用';
  editingId.value = app.id;
  formModel.nickname = app.nickname;
  showModal.value = true;
}

// 删除应用
async function handleDeleteApp(id: number) {
  startLoading();
  try {
    const { error } = await fetchDeleteApp(id);
    if (!error) {
      window.$message?.success('删除成功');
      await getAppData();
    } else {
      window.$message?.error('删除失败');
    }
  } catch (error) {
    console.error('删除应用失败:', error);
    window.$message?.error('删除失败');
  } finally {
    endLoading();
  }
}

// 重置表单
function resetForm() {
  formModel.nickname = '';
  editingId.value = null;
}

// 关闭弹窗
function closeModal() {
  showModal.value = false;
  resetForm();
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    startLoading();
    
    if (isEdit.value && editingId.value) {
      // 编辑应用
      const { error } = await fetchUpdateApp({
        id: editingId.value,
        nickname: formModel.nickname
      });
      
      if (!error) {
        window.$message?.success('编辑成功');
        await getAppData();
        closeModal();
      } else {
        window.$message?.error('编辑失败');
      }
    } else {
      // 添加应用
      const { error } = await fetchCreateApp({
        nickname: formModel.nickname
      });
      
      if (!error) {
        window.$message?.success('添加成功');
        await getAppData();
        closeModal();
      } else {
        window.$message?.error('添加失败');
      }
    }
  } catch (error) {
    // 表单验证失败
  } finally {
    endLoading();
  }
}
</script>

<style scoped>
.card-wrapper {
  @apply flex-col-stretch gap-16px overflow-hidden;
}
</style>

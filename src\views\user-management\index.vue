<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <NCard title="用户管理" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <div class="flex items-center gap-12px">
          <!-- 调试信息 -->
          <NTag type="info" size="small">
            当前用户角色: {{ authStore.userInfo.role.join(', ') || '无' }}
          </NTag>
          <NButton type="primary" size="small" @click="handleAddUser">
            <template #icon>
              <icon-ic-round-plus class="text-icon" />
            </template>
            添加用户
          </NButton>
        </div>
      </template>
      <div class="h-full flex-col">
        <NDataTable
          :columns="columns"
          :data="data"
          size="small"
          :flex-height="!appStore.isMobile"
          :scroll-x="962"
          :loading="loading"
          remote
          :row-key="row => row.id"
          :pagination="appStore.isMobile ? mobilePagination : pagination"
          class="sm:h-full"
        />
      </div>
    </NCard>

    <!-- 添加/编辑用户弹窗 -->
    <NModal v-model:show="showModal" preset="card" :title="modalTitle" class="w-700px">
      <NForm ref="formRef" :model="formModel" :rules="rules" label-placement="left" :label-width="80">
        <div class="flex-col gap-16px">
          <NFormItem label="用户名" path="username">
            <NInput v-model:value="formModel.username" placeholder="请输入用户名" />
          </NFormItem>
          <NFormItem label="邮箱" path="email">
            <NInput v-model:value="formModel.email" placeholder="请输入邮箱" />
          </NFormItem>
          <NFormItem label="手机号" path="phone">
            <NInput v-model:value="formModel.phone" placeholder="请输入手机号" />
          </NFormItem>
          <NFormItem label="角色" path="role">
            <NSelect
              v-model:value="formModel.role"
              placeholder="请选择角色"
              :options="roleOptions"
            />
          </NFormItem>
          <NFormItem label="状态" path="status">
            <NSelect
              v-model:value="formModel.status"
              placeholder="请选择状态"
              :options="statusOptions"
            />
          </NFormItem>
        </div>
      </NForm>
      <template #footer>
        <div class="flex-y-center justify-end gap-12px">
          <NButton @click="closeModal">取消</NButton>
          <NButton type="primary" @click="handleSubmit">确定</NButton>
        </div>
      </template>
    </NModal>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, computed, onMounted } from 'vue';
import type { Ref } from 'vue';
import { NButton, NPopconfirm, NTag } from 'naive-ui';
import type { DataTableColumns, FormInst, FormRules } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { useAuthStore } from '@/store/modules/auth';
import { useLoading } from '@sa/hooks';
import { $t } from '@/locales';
import { fetchUserData } from '@/service/api';
import { Api } from '@/typings/api';
import { formatDateTime } from '@/utils/common';

defineOptions({
  name: 'UserManagement'
});

const appStore = useAppStore();
const authStore = useAuthStore();

// interface UserData {
//   id: number;
//   username: string;
//   email: string;
//   phone: string;
//   role: string;
//   status: 'active' | 'inactive';
//   is_active: number;
//   createTime: string;
// }

interface FormModel {
  username: string;
  email: string;
  phone: string;
  role: string;
  status: 'active' | 'inactive';
}

const { loading, startLoading, endLoading } = useLoading(false);

// 表格数据
const data = ref<Api.Auth.UserListItem[]>([]);

// 分页状态
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 50],
  showQuickJumper: true,
  onUpdatePage: (page: number) => {
    pagination.page = page;
    getUserData();
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    getUserData();
  }
});

// 表格列配置
const columns: Ref<DataTableColumns<Api.Auth.UserListItem>> = ref([
  {
    type: 'selection'
  },
  {
    key: 'index',
    title: '序号',
    width: 80,
    align: 'center',
    render: (_, index) => {
      return (pagination.page - 1) * pagination.pageSize + index + 1;
    }
  },
  {
    key: 'id',
    title: 'ID',
    width: 80,
    align: 'center'
  },
  {
    key: 'nickname',
    title: '昵称',
    width: 100,
    ellipsis: {
      tooltip: true
    }
  },
  {
    key: 'phone',
    title: '手机号',
    width: 140
  },
  {
    key: 'balance',
    title: '积分',
    width: 140
  },
  {
    key: 'role',
    title: '角色',
    width: 100,
    render: row => {
      const roleMap: Record<string, { label: string; type: 'success' | 'warning' | 'error' }> = {
        admin: { label: '管理员', type: 'error' },
        user: { label: '普通用户', type: 'success' },
        guest: { label: '访客', type: 'warning' }
      };
      const role = roleMap[row.role] || { label: row.role, type: 'success' };
      return <NTag type={role.type}>{role.label}</NTag>;
    }
  },
  {
    key: 'is_active',
    title: '状态',
    width: 100,
    render: row => {
      const statusMap: Record<number, { label: string; type: 'success' | 'error' }> = {
        1: { label: '启用', type: 'success' as const },
        0: { label: '禁用', type: 'error' as const }
      };
      const status = statusMap[row.is_active] || { label: '未知', type: 'error' as const };
      return <NTag type={status.type}>{status.label}</NTag>;
    }
  },
  {
    key: 'created_at',
    title: '创建时间',
    width: 180,
    render: row => formatDateTime(row.created_at)
  },
  {
    key: 'actions',
    title: '操作',
    width: 160,
    align: 'center',
    render: row => (
      <div class="flex-y-center gap-8px">
        <NButton size="small" type={row.is_active === 1 ? 'warning' : 'success'} onClick={() => handleToggleUserStatus(row)}>
          {row.is_active === 1 ? '冻结' : '解冻'}
        </NButton>
        <NButton size="small" type="primary" onClick={() => handleRecharge(row)}>
          充值
        </NButton>
      </div>
    )
  }
]) as Ref<DataTableColumns<Api.Auth.UserListItem>>;

// 移动端分页
const mobilePagination = computed(() => {
  if (appStore.isMobile) {
    return {
      ...pagination,
      pageSize: 10,
      showSizePicker: false,
      showQuickJumper: false
    };
  }
  return false;
});

// 弹窗相关
const showModal = ref(false);
const modalTitle = ref('');
const formRef = ref<FormInst | null>(null);
const isEdit = ref(false);
const editingId = ref<number | null>(null);

// 表单数据
const formModel = reactive<FormModel>({
  username: '',
  email: '',
  phone: '',
  role: '',
  status: 'active'
});

// 表单验证规则
const rules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
};

// 角色选项
const roleOptions = [
  { label: '管理员', value: 'admin' },
  { label: '普通用户', value: 'user' },
  { label: '访客', value: 'guest' }
];

// 状态选项
const statusOptions = [
  { label: '启用', value: 'active' },
  { label: '禁用', value: 'inactive' }
];

// 获取用户数据
async function getUserData() {
  startLoading();
  try {
    const { data: userResponse, error } = await fetchUserData(
      pagination.page,
      pagination.pageSize,
      'user',
      1
    );

    if (!error) {
      //列表数据
      data.value = userResponse.list;
      // 更新分页信息
      pagination.total = userResponse.total;
    }
  } catch (error) {
    console.error('获取用户数据失败:', error);
    window.$message?.error('获取用户数据失败');
    data.value = [];
  } finally {
    endLoading();
  }
}

// 添加用户
function handleAddUser() {
  isEdit.value = false;
  modalTitle.value = '添加用户';
  resetForm();
  showModal.value = true;
}

// 切换用户状态（冻结/解冻）
async function handleToggleUserStatus(row: Api.Auth.UserListItem) {
  startLoading();
  try {
    // // 模拟API调用
    // await new Promise(resolve => setTimeout(resolve, 500));

    // // 更新用户状态
    // const index = data.value.findIndex(item => item.id === row.id);
    // if (index > -1) {
    //   data.value[index].is_active = row.is_active === 1 ? 0 : 1;
    // }

    // const statusText = row.is_active === 1 ? '冻结' : '解冻';
    // window.$message?.success(`${statusText}成功`);

    const confirmText = row.is_active === 1 ? 'common.freezeConfirm' : 'common.unfreezeConfirm';
    if(row.is_active === 1){
      // 冻结用户
      window.$dialog?.warning({
        title: $t('common.tip'),
        content: $t(confirmText),
        positiveText: $t('common.confirm'),
        negativeText: $t('common.cancel'),
        onPositiveClick: () => {

        }
      });
    }else{
      // 解冻用户
      window.$dialog?.success({
        title: $t('common.tip'),
        content: $t(confirmText),
        positiveText: $t('common.confirm'),
        negativeText: $t('common.cancel'),
        onPositiveClick: () => {

        }
      });
    }
  } catch (error) {
    // console.error('切换用户状态失败:', error);
    // window.$message?.error('操作失败');
  } finally {
    endLoading();
  }
}

// 用户充值
function handleRecharge(row: Api.Auth.UserListItem) {
  window.$message?.info(`为用户 ${row.nickname || row.phone} 充值功能待开发`);
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    startLoading();
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    
    if (isEdit.value && editingId.value) {
      // 编辑用户
      const index = data.value.findIndex(item => item.id === editingId.value);
      if (index > -1) {
        Object.assign(data.value[index], formModel);
      }
      window.$message?.success('编辑成功');
    } else {
      // 添加用户
      // const newUser: UserData = {
      //   id: Date.now(),
      //   ...formModel,
      //   is_active: formModel.status === 'active' ? 1 : 0,
      //   createTime: new Date().toLocaleString()
      // };
      //data.value.unshift(newUser);
      window.$message?.success('添加成功');
    }
    
    closeModal();
  } catch (error) {
    // 表单验证失败
  } finally {
    endLoading();
  }
}

// 重置表单
function resetForm() {
  Object.assign(formModel, {
    username: '',
    email: '',
    phone: '',
    role: '',
    status: 'active'
  });
}

// 关闭弹窗
function closeModal() {
  showModal.value = false;
  resetForm();
  editingId.value = null;
}

// 初始化
onMounted(() => {
  getUserData();
});
</script>

<style scoped>
.card-wrapper {
  @apply rd-8px shadow-sm;
}
</style>
